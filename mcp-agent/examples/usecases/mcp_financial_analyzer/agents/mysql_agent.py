"""
MySQL Agent for historical financial data analysis integration with MySQL MCP server.
Follows the reference pattern from dev_agent/db_agent_develop/MySQL/client.py for MCP orchestration.
"""

import os
import logging
import asyncio
from dataclasses import dataclass
from typing import Union as TypingUnion

# Configure logging
logger = logging.getLogger("mysql_analyzer")

try:
    # Import atomic-agents components (matches reference pattern)
    from atomic_agents.lib.factories.mcp_tool_factory import fetch_mcp_tools
    from atomic_agents.agents.base_agent import BaseAgent, BaseAgentConfig, BaseIOSchema
    from atomic_agents.lib.components.system_prompt_generator import SystemPromptGenerator
    from atomic_agents.lib.components.agent_memory import AgentMemory
    import instructor
    from openai import OpenAI
    from pydantic import Field
    ATOMIC_COMPONENTS_AVAILABLE = True
except Exception as e:
    logger.warning(f"Failed to import atomic-agents components: {e}")
    ATOMIC_COMPONENTS_AVAILABLE = False
    fetch_mcp_tools = None
    BaseAgent = None
    BaseAgentConfig = None
    BaseIOSchema = None
    SystemPromptGenerator = None
    AgentMemory = None
    instructor = None
    openai = None
    OpenAI = None


@dataclass
class MCPConfig:
    """Configuration for the MCP Orchestrator (SSE transport)."""

    mcp_server_url: str = os.getenv("MYSQL_MCP_SERVER_URL", "http://localhost:8702")
    vllm_api_base: str = os.getenv("VLLM_API_BASE", "http://************:38701/v1")
    vllm_model: str = os.getenv("VLLM_MODEL", "Qwen/Qwen3-32B")
    vllm_api_key: str = os.getenv("VLLM_API_KEY", "EMPTY")


config = MCPConfig()

# Build vLLM client via instructor (updated to use vLLM instead of OpenAI)
client = None
if ATOMIC_COMPONENTS_AVAILABLE:
    try:
        client = instructor.from_openai(
            OpenAI(
                base_url=config.vllm_api_base,
                api_key=config.vllm_api_key
            )
        )
        logger.info(f"✓ vLLM client initialized: {config.vllm_api_base} with model {config.vllm_model}")
    except Exception as _e:
        logger.warning(f"Failed to initialize vLLM client for orchestrator: {_e}")
        client = None


# Final response schema (matches reference)
class FinalResponseSchema(BaseIOSchema):
    """Schema for providing a final text response to the user."""

    response_text: str = Field(..., description="The final text response to the user's query")


# Global variables for lazy initialization
tools = []
tool_schema_to_class_map = {}
tool_input_schemas = tuple()
available_schemas = (FinalResponseSchema,)
ActionUnion = TypingUnion[FinalResponseSchema]

def _initialize_tools():
    """Lazy initialization of MCP tools to avoid hanging during import."""
    global tools, tool_schema_to_class_map, tool_input_schemas, available_schemas, ActionUnion

    if tools or not ATOMIC_COMPONENTS_AVAILABLE:
        return  # Already initialized or not available

    try:
        # Check if we're running in an async context
        try:
            asyncio.get_running_loop()
            # We're in an async context - skip tool initialization for now
            # Tools will be initialized when actually needed
            logger.info("Detected running event loop, deferring MCP tool initialization...")
            _initialize_tools_sync_fallback()
        except RuntimeError:
            # No running loop, safe to use asyncio.run()
            tools = fetch_mcp_tools(
                mcp_endpoint=config.mcp_server_url,
                use_stdio=False,
            )
            _process_fetched_tools()

    except Exception as e:
        logger.warning(f"Failed to fetch MCP tools: {e}")
        tools = []

def _initialize_tools_sync_fallback():
    """Fallback initialization when in async context - creates a custom MCP tool."""
    global tools, tool_schema_to_class_map, tool_input_schemas, available_schemas, ActionUnion

    logger.info("Creating custom MCP tool for async context...")

    try:
        # Create a custom tool class that handles MCP calls directly
        from pydantic import BaseModel, Field

        class MySQLAgentInputSchema(BaseModel):
            """Input schema for MySQL agent tool."""
            query: str = Field(..., description="SQL query to execute")

        class MySQLAgentOutputSchema(BaseModel):
            """Output schema for MySQL agent tool."""
            result: str = Field(..., description="Query result")

        class CustomMySQLTool:
            """Custom MySQL tool that handles async MCP calls."""
            input_schema = MySQLAgentInputSchema
            output_schema = MySQLAgentOutputSchema
            mcp_tool_name = "mysql_agent"

            def run(self, input_data):
                """Execute the MySQL query via direct MCP call."""
                try:
                    # Import MCP client components
                    from mcp.client.sse import sse_client
                    from mcp import ClientSession
                    import asyncio

                    async def execute_query():
                        sse_endpoint = f"{config.mcp_server_url}/sse"
                        async with sse_client(sse_endpoint) as (read_stream, write_stream):
                            async with ClientSession(read_stream, write_stream) as session:
                                await session.initialize()
                                result = await session.call_tool(
                                    name="mysql_agent",
                                    arguments={"input_data": {"query": input_data.query}}
                                )
                                return result

                    # Check if we're in an async context
                    try:
                        asyncio.get_running_loop()
                        # We're in an async context, create a task
                        import concurrent.futures
                        with concurrent.futures.ThreadPoolExecutor() as executor:
                            future = executor.submit(asyncio.run, execute_query())
                            result = future.result()
                    except RuntimeError:
                        # No running loop, safe to use asyncio.run
                        result = asyncio.run(execute_query())

                    # Process the result - handle TextContent objects
                    if hasattr(result, 'content'):
                        content = result.content
                        # If content is a list of TextContent objects, extract text
                        if isinstance(content, list):
                            text_parts = []
                            for item in content:
                                if hasattr(item, 'text'):
                                    text_parts.append(item.text)
                                else:
                                    text_parts.append(str(item))
                            content = '\n'.join(text_parts)
                    elif isinstance(result, dict) and 'content' in result:
                        content = result['content']
                        # Handle list of TextContent in dict format
                        if isinstance(content, list):
                            text_parts = []
                            for item in content:
                                if isinstance(item, dict) and 'text' in item:
                                    text_parts.append(item['text'])
                                elif hasattr(item, 'text'):
                                    text_parts.append(item.text)
                                else:
                                    text_parts.append(str(item))
                            content = '\n'.join(text_parts)
                    else:
                        content = str(result)

                    return MySQLAgentOutputSchema(result=content)

                except Exception as e:
                    logger.error(f"Error executing MySQL query: {e}")
                    return MySQLAgentOutputSchema(result=f"Error: {str(e)}")

        # Set up the tool mapping
        tools = [CustomMySQLTool]
        tool_schema_to_class_map = {MySQLAgentInputSchema: CustomMySQLTool}
        tool_input_schemas = (MySQLAgentInputSchema,)
        available_schemas = tool_input_schemas + (FinalResponseSchema,)
        ActionUnion = TypingUnion[*available_schemas]

        logger.info("Custom MySQL tool created successfully")

    except Exception as e:
        logger.warning(f"Failed to create custom MySQL tool: {e}")
        # Create minimal fallback
        tools = []
        tool_schema_to_class_map = {}
        tool_input_schemas = tuple()
        available_schemas = (FinalResponseSchema,)
        ActionUnion = TypingUnion[FinalResponseSchema]

def _process_fetched_tools():
    """Process the fetched tools and set up schemas."""
    global tool_schema_to_class_map, tool_input_schemas, available_schemas, ActionUnion

    if not tools:
        logger.warning("No MCP tools found. Please ensure the MySQL MCP server is running and accessible.")
        return

    # Build mapping from input_schema to ToolClass (matches reference)
    tool_schema_to_class_map = {
        ToolClass.input_schema: ToolClass for ToolClass in tools if hasattr(ToolClass, "input_schema")
    }

    # Collect all tool input schemas (matches reference)
    tool_input_schemas = tuple(tool_schema_to_class_map.keys())
    logger.info(f"Tool input schemas: {[schema.__name__ for schema in tool_input_schemas]}")

    # Available schemas include all tool input schemas and the final response schema (matches reference)
    available_schemas = tool_input_schemas + (FinalResponseSchema,)
    logger.info(f"Available schemas: {[schema.__name__ for schema in available_schemas]}")

    # Define the Union of all action schemas (matches reference)
    ActionUnion = TypingUnion[*available_schemas]


def safe_orchestrator_run(agent, *args, **kwargs):
    """Run orchestrator with retries and inline validation feedback (matches reference)."""
    # Initialize tools lazily
    _initialize_tools()

    max_retry = 20
    example_schema = {
        "reasoning": "To list all tables, I will use SHOW TABLES.",
        "action": {
            "tool_name": "mysql_agent",
            "input_data": {
                "query": "SHOW TABLES;"
            }
        }
    }
    for _ in range(max_retry):
        try:
            output = agent.run(*args, **kwargs)
            if hasattr(output, "action") and output.action is not None:
                return output
            else:
                agent.memory.add_message(
                    "system",
                    {"query": (
                        "【格式錯誤提醒】Your last output missed the 'action' field!\n"
                        "你的 output 必須同時包含 'reasoning'（推理過程）和 'action'（執行動作），action 必須是 tool input schema 或 FinalResponseSchema。\n"
                        "請嚴格按照下方 JSON 格式產生 output：\n"
                        f"{example_schema}\n"
                        "If you see a validation error, immediately retry with the correct action format.\n"
                    )},
                )
        except Exception as e:
            agent.memory.add_message(
                "system",
                {"query": (
                    "【Validation Error】Your previous output caused a validation error!\n"
                    "錯誤訊息如下（請他媽參考並立即修正 output 格式）：\n"
                    f"{str(e)}\n"
                    "正確格式請參考範例：\n"
                    f"{example_schema}\n"
                )},
            )
    raise RuntimeError("LLM failed to output correct schema after fallback.")


# Schema definitions (matches reference)
class MCPOrchestratorInputSchema(BaseIOSchema):
    """Input schema for the MCP Orchestrator Agent."""

    query: str = Field(..., description="The user's query to analyze.")


def create_orchestrator_output_schema():
    """Create the output schema dynamically after tools are initialized."""
    global ActionUnion

    class OrchestratorOutputSchema(BaseIOSchema):
        """Output schema for the orchestrator. Contains reasoning and the chosen action."""

        reasoning: str = Field(
            ..., description="Detailed explanation of why this action was chosen and how it will address the user's query."
        )
        action: ActionUnion = Field(  # type: ignore[reportInvalidTypeForm]
            ..., description="The chosen action: either a tool's input schema instance or a final response schema instance."
        )

    return OrchestratorOutputSchema

# Main logic and script entry point (matches reference pattern)
def create_mysql_orchestrator_agent():
    """
    Create a MySQL MCP Orchestrator Agent following the reference pattern.

    Returns:
        BaseAgent configured for MySQL material shortage analysis
    """
    if not ATOMIC_COMPONENTS_AVAILABLE:
        raise RuntimeError("atomic-agents components not available. Please install atomic-agents.")

    # Initialize tools lazily
    _initialize_tools()

    # Create the output schema after tools are initialized
    OrchestratorOutputSchema = create_orchestrator_output_schema()

    try:
        logger.info("Initializing MySQL MCP Agent System (SSE mode)...")

        # Create and initialize orchestrator agent (matches reference)
        logger.info("Creating orchestrator agent...")
        memory = AgentMemory()
        orchestrator_agent = BaseAgent(
            BaseAgentConfig(
                client=client,
                model=config.vllm_model,
                memory=memory,
                system_prompt_generator=SystemPromptGenerator(
                    background=[
                        "You are a MySQL MCP Orchestrator Agent, designed to chat with users and",
                        "determine the best way to handle their queries using the available MySQL tools.",
                        "You can interact with the mysql_agent tool to execute SQL queries for material shortage analysis.",
                        "Please check the table's names and their schemas before executing any queries.",
                        """
                        When you call the `mysql_agent` tool, you MUST provide the input as a dictionary with the exact format:
                        {'input_data': {'query': '<YOUR_SQL_QUERY>'}, 'tool_name': 'mysql_agent'}

                        - Only the key 'query' is allowed under 'input_data'.
                        - Do NOT use other keys such as 'sql', 'table', or plain strings.
                        - All SQL statements (e.g., SHOW TABLES, DESCRIBE, SELECT...) must be placed in the value of 'query'.
                        """,
                        """
                        When returning a reasoning step, you must always include an 'action' field.
                        The 'action' field should be a tool call (e.g., mysql_agent tool call) or a FinalResponseSchema if you are providing the final answer.
                        Make sure to always include both 'reasoning' and 'action' fields in your output.

                        Example:
                        {
                            "reasoning": "To determine the columns available in CUSTOMER_ORDERS, I will use the DESCRIBE statement.",
                            "action": {
                                "tool_name": "mysql_agent",
                                "input_data": {
                                    "query": "DESCRIBE CUSTOMER_ORDERS;"
                                }
                            }
                        }
                        """,
                        "You can handle material shortage (out-of-stock) analysis for new orders.",
                        "When a user asks if a new order will cause material shortage:",
                        "1. Check the current stock (inventory) for each material required by the order.",
                        "2. If any material's current stock is less than the order's required quantity, a shortage has occurred.",
                        "3. When a shortage is detected, prepare an email including:",
                        "   - material_name",
                        "   - material_code",
                        "   - current_stock",
                        "   - required_quantity for the new order.",
                        "4. If the responsible staff is unknown, always send the email to Jim Xiao.",
                        "5. When sending an email, only output the email content.",
                        "6. Please remember to email the staff if there is a shortage, and confirm to the user if all materials are sufficient. So that they can take action.",
                        "You specialize in historical supplier data analysis and material availability tracking.",
                        "For queries about material codes (e.g., DGRA00748) or order numbers (e.g., CUSTORD-202506001), use appropriate SQL queries to find:",
                        "- Supplier reliability and delivery performance",
                        "- Material availability and stock levels",
                        "- Order history and shipping addresses",
                        "- Historical shortage patterns for risk assessment",
                    ],
                    steps=[
                        "1. Use the reasoning field to determine if one or more successive tool calls could be used to handle the user's query.",
                        "2. If so, choose the appropriate tool(s) one at a time and extract all necessary parameters from the query.",
                        "3. If a single tool can not be used to handle the user's query, think about how to break down the query into "
                        "smaller tasks and route them to the appropriate tool(s).",
                        "4. If no sequence of tools could be used, or if you are finished processing the user's query, provide a final "
                        "response to the user.",
                        "5. Receive the user query about potential material shortage for a new order.",
                        "6. Look up required materials and their current stock.",
                        "7. Compare current_stock with the order's required_quantity for each material.",
                        "8. If current_stock < required_quantity, it's a shortage.",
                        "9. For any shortage, prepare and output the email content to notify the staff (to Jim Xiao if unsure).",
                        "10. If no shortage, confirm to the user that all materials are sufficient.",
                        "11. For historical analysis queries, use appropriate SQL to find supplier reliability and material availability.",
                        "12. For order lookup queries, find shipping addresses and delivery information.",
                    ],
                    output_instructions=[
                        "Every output MUST have both 'reasoning' and 'action' fields, or it will result in a validation error.",
                        "Never omit the 'action' field; if you're done, use FinalResponseSchema.",
                        "1. Always provide a detailed explanation of your decision-making process in the 'reasoning' field.",
                        "2. Choose exactly one action schema (either a tool input or FinalResponseSchema).",
                        "3. Ensure all required parameters for the chosen tool are properly extracted and validated.",
                        "4. Maintain a professional and helpful tone in all responses.",
                        "5. Break down complex queries into sequential tool calls before giving the final answer via `FinalResponseSchema`.",
                        "6. Each round, you can only execute one tool call or give a final answer. Do not perform multiple steps at once.",
                        "7. If unsure about table or column names, always use list_tables or describe_table tools first, then construct the next query based on the results.",
                        "8. Never execute a SELECT SQL by guessing; always confirm the schema first.",
                        "9. If you receive an SQL error (e.g., 'table doesn't exist' or 'unknown column'), use list_tables or describe_table to find the correct table or column.",
                        "10. You must only send valid MySQL SQL queries (such as SHOW TABLES;, DESCRIBE tablename;) when using the mysql_agent tool. Do not use tool names as SQL commands.",
                        "11. Your output MUST be a JSON object with both a 'reasoning' and 'action' field.",
                        "12. The 'action' field must be a tool call (e.g., mysql_agent) with all required parameters, or a FinalResponseSchema if you are answering the user directly.",
                        "13. Never omit the 'action' field, even for intermediate steps such as listing tables or describing a table.",
                        "14. Example: {'reasoning': 'To list all tables...', 'action': {'tool_name': 'mysql_agent', 'input_data': {'query': 'SHOW TABLES;'}}}",
                        "15. If you receive a validation error, immediately retry with the correct action format.",
                        "When a shortage occurs, only output the email notification content. Example:",
                        "Subject: Material Shortage Alert",
                        "To: Jim Xiao",
                        "Body: Material [material_name] ([material_code]) is short for the new order. Current stock: [current_stock]. Required: [required_quantity]. Please arrange replenishment.",
                        "If no shortage, reply to user with normal action.",
                    ],
                ),
                input_schema=MCPOrchestratorInputSchema,
                output_schema=OrchestratorOutputSchema,
            )
        )
        logger.info("Successfully created MySQL orchestrator agent.")
        return orchestrator_agent

    except Exception as e:
        logger.error(f"Failed to create MySQL orchestrator agent: {e}")
        raise


# Interactive chat function (matches reference pattern)
def run_mysql_chat():
    """
    Run interactive chat with MySQL MCP Orchestrator Agent.
    Matches the reference pattern from client.py.
    """
    if not ATOMIC_COMPONENTS_AVAILABLE:
        print("Error: atomic-agents components not available. Please install atomic-agents.")
        return

    # Initialize tools lazily
    _initialize_tools()

    try:
        # Create the orchestrator agent
        orchestrator_agent = create_mysql_orchestrator_agent()

        print("MySQL MCP Agent Interactive Chat (SSE mode). Type 'exit' or 'quit' to leave.")
        while True:
            query = input("You: ").strip()
            if query.lower() in {"exit", "quit"}:
                print("Exiting chat. Goodbye!")
                break
            if not query:
                continue  # Ignore empty input

            try:
                # Initial run with user query
                orchestrator_output = safe_orchestrator_run(orchestrator_agent, MCPOrchestratorInputSchema(query=query))

                action_instance = orchestrator_output.action
                reasoning = orchestrator_output.reasoning
                print(f"Orchestrator reasoning: {reasoning}")

                # Keep executing until we get a final response
                while not isinstance(action_instance, FinalResponseSchema):
                    schema_type = type(action_instance)
                    ToolClass = tool_schema_to_class_map.get(schema_type)
                    if not ToolClass:
                        raise ValueError(f"Unknown schema type '{schema_type.__name__}' returned by orchestrator")

                    tool_name = ToolClass.mcp_tool_name
                    print(f"Executing tool: {tool_name}")
                    print(f"Parameters: {action_instance.model_dump()}")

                    tool_instance = ToolClass()
                    tool_output = tool_instance.run(action_instance)
                    print(f"Result: {tool_output.result}")

                    # Add tool result to agent memory
                    result_message = MCPOrchestratorInputSchema(
                        query=(f"Tool {tool_name} executed with result: {tool_output.result}")
                    )
                    orchestrator_agent.memory.add_message("system", result_message)

                    # Run the agent again without parameters to continue the flow
                    orchestrator_output = safe_orchestrator_run(orchestrator_agent)
                    action_instance = orchestrator_output.action
                    reasoning = orchestrator_output.reasoning
                    print(f"Orchestrator reasoning: {reasoning}")

                # Final response from the agent
                print(f"Agent: {action_instance.response_text}")

            except Exception as e:
                print(f"Error processing query: {str(e)}")

    except Exception as e:
        print(f"Fatal error: {str(e)}")


# Main entry point
if __name__ == "__main__":
    run_mysql_chat()